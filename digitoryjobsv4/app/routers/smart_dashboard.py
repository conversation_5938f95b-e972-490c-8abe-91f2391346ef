"""
Smart Dashboard Router - LLM-powered dashboard generation
"""
import os
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from dotenv import load_dotenv

from app.utility.report import grnStatusReport, store_variance, inventoryConsumptionNew
from app.utility.dashboard_agents import (
    smart_ask_dashboard,
    generate_purchase_dashboard,
    generate_inventory_dashboard,
    generate_reconciliation_dashboard
)
from app.database import roloposconfigsCol
import requests

load_dotenv()

# ===== ROUTER SETUP =====
router = APIRouter()
security = HTTPBearer()

# ===== AUTHENTICATION =====
async def authenticate(credentials: HTTPAuthorizationCredentials = Depends(security)) -> str:
    """Authenticate requests using Bearer token"""
    if not credentials:
        raise HTTPException(status_code=401, detail="Not authenticated")

    expected_token = os.getenv("BEARER_TOKEN")
    if credentials.credentials != expected_token:
        raise HTTPException(status_code=401, detail="Invalid token")

    return credentials.credentials

# ===== CONFIGURATION =====
DASHBOARD_CONFIG = {
    "chart_colors": [
        '#ff8c42',  # Orange primary
        '#ffb366',  # Orange light
        '#87a96b',  # Sage green
        '#6b9bd2',  # Soft blue
        '#9b7bb8',  # Muted purple
        '#8d7b68',  # Warm gray
        '#d4a5a5',  # Dusty rose
        '#ffc999',  # Orange lighter
        '#a4c085',  # Sage green light
        '#85aedb',  # Soft blue light
        '#af95c6',  # Muted purple light
        '#a69082',  # Warm gray light
        '#ddb8b8',  # Dusty rose light
        '#ffe0cc',  # Orange lightest
        '#f4a261'   # Warning orange
    ],
    "chart_types": {
        "bar": "Bar Chart",
        "horizontalBar": "Horizontal Bar Chart",
        "line": "Line Chart",
        "doughnut": "Doughnut Chart",
        "pie": "Pie Chart",
        "radar": "Radar Chart",
        "polarArea": "Polar Area Chart"
    },
    "currency": {"code": "INR", "symbol": "₹"},
    "dashboard_types": [
        {"value": "inventory", "label": "Inventory Dashboard"},
        {"value": "purchase", "label": "Purchase Dashboard"},
        {"value": "reconciliation", "label": "COGS Dashboard"},
    ],
    "base_date_options": [
        {"value": "deliveryDate", "label": "GRN Date(System Entry Date)"},
        {"value": "invoiceDate", "label": "Vendor Invoice Date"},
        {"value": "grnDate", "label": "Goods Received Date"}
    ],
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "default_chart_options": {
        "responsive": True,
        "maintainAspectRatio": False,
        "plugins": {
            "legend": {
                "display": True,
                "position": "top",
                "labels": {
                    "usePointStyle": True,
                    "padding": 15,
                    "font": {"size": 11}
                }
            },
            "tooltip": {
                "backgroundColor": "rgba(255, 255, 255, 0.95)",
                "titleColor": "#333",
                "bodyColor": "#666",
                "borderColor": "#ffb366",
                "borderWidth": 2,
                "cornerRadius": 6
            }
        },
        "scales": {
            "x": {
                "grid": {"display": False},
                "ticks": {"font": {"size": 10}}
            },
            "y": {
                "beginAtZero": True,
                "grid": {"color": "#e9ecef"},
                "ticks": {"font": {"size": 10}}
            }
        }
    },
    "summary_card_config": {
        "colors": {
            "currency": "#ffb366",
            "number": "#ff9d4d",
            "percentage": "#ffc999",
            "text": "#6c757d"
        },
        "icons": {
            "currency": "account_balance_wallet",
            "number": "analytics",
            "percentage": "percent",
            "text": "info"
        }
    },
    "ui_config": {
        "default_date_range_days": 30,
        "default_dashboard_type": "inventory",
        "default_base_date": "deliveryDate"
    }
}

# ===== ROUTES =====
@router.get("/config")
async def get_dashboard_config(_: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get global dashboard configuration for dynamic frontend rendering"""
    return {"status": "success", "data": DASHBOARD_CONFIG}



@router.get("/departments/{tenant_id}")
async def get_departments(tenant_id: str, _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get departments from RMS API for a tenant (secure backend implementation)"""
    try:
        # Fetch tenant credentials from MongoDB
        config = roloposconfigsCol.find_one({"tenantId": tenant_id})
        if not config:
            return {"status": "error", "message": "Tenant configuration not found"}

        # Extract RMS API credentials (stored securely in backend)
        credentials = {
            "emailId": config.get('rmsApiEmail', '<EMAIL>'),
            "password": config.get('rmsApiPassword', 'Ds@2024')
        }

        # Authenticate with RMS API
        rms_api_url = "https://rms-api.digitory.com"
        login_response = requests.post(
            f"{rms_api_url}/login",
            json=credentials,
            headers={"App-Id": "rms-web-app"},
            timeout=30
        )

        if login_response.status_code != 200:
            return {"status": "error", "message": "RMS API authentication failed"}

        auth_data = login_response.json()
        token = auth_data.get("token")
        account_id = auth_data.get("accountId")

        if not token or not account_id:
            return {"status": "error", "message": "Invalid RMS API authentication response"}

        # Fetch departments from RMS API
        departments_response = requests.get(
            f"{rms_api_url}/account/{account_id}/departments",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            timeout=30
        )

        if departments_response.status_code != 200:
            return {"status": "error", "message": "Failed to fetch departments from RMS API"}

        departments_data = departments_response.json()

        # Format departments for frontend
        departments = []
        if isinstance(departments_data, list):
            for dept in departments_data:
                departments.append({
                    "id": dept.get("id") or dept.get("_id"),
                    "name": dept.get("name") or dept.get("departmentName"),
                    "code": dept.get("code") or dept.get("departmentCode"),
                    "description": dept.get("description"),
                    "isActive": dept.get("isActive", True)
                })

        return {"status": "success", "data": departments}

    except requests.exceptions.RequestException as e:
        print(f"RMS API request error: {str(e)}")
        return {"status": "error", "message": "Failed to connect to RMS API"}
    except Exception as e:
        print(f"Error fetching departments: {str(e)}")
        return {"status": "error", "message": "Failed to fetch departments"}


@router.get("/department-category-mappings/{tenant_id}")
async def get_department_category_mappings(tenant_id: str, _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Get department-category mappings for a tenant"""
    try:
        # For now, return empty array - this will be implemented when we have a storage mechanism
        # In a real implementation, this would fetch from a database table
        return {"status": "success", "data": []}
    except Exception as e:
        print(f"Error fetching department-category mappings: {str(e)}")
        return {"status": "error", "message": "Failed to fetch mappings"}


@router.post("/department-category-mappings")
async def save_department_category_mappings(request: Dict[str, Any], _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Save department-category mappings for a tenant"""
    try:
        tenant_id = request.get('tenantId', '')
        mappings = request.get('mappings', [])

        if not tenant_id:
            return {"status": "error", "message": "Tenant ID is required"}

        # For now, just return success - this will be implemented when we have a storage mechanism
        # In a real implementation, this would save to a database table
        print(f"Saving department-category mappings for tenant {tenant_id}: {mappings}")

        return {"status": "success", "message": "Mappings saved successfully"}
    except Exception as e:
        print(f"Error saving department-category mappings: {str(e)}")
        return {"status": "error", "message": "Failed to save mappings"}


@router.post("/smart_ask")
async def smart_ask(request: Dict[str, Any], _: str = Depends(authenticate)) -> Dict[str, Any]:
    """Generate smart dashboard based on user query and filters"""
    try:
        # Extract request parameters
        filters = request.get('filters', {})
        user_query = request.get('user_query', '')
        tenant_id = request.get('tenant_id', '')
        use_default_charts = request.get('use_default_charts', False)
        dashboard_type = request.get('dashboard_type', 'purchase')

        # Build job configuration
        job = _build_job_config(tenant_id, filters)

        # Generate dashboard based on type
        dashboard_data = _generate_dashboard(job, dashboard_type, user_query, use_default_charts, tenant_id)

        return {"status": "success", "data": dashboard_data}

    except ValueError as e:
        raise HTTPException(status_code=400, detail=f"Invalid request data: {str(e)}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# ===== HELPER FUNCTIONS =====
def _build_job_config(tenant_id: str, filters: Dict[str, Any]) -> Dict[str, Any]:
    """Build job configuration from request parameters"""
    try:
        return {
            'tenantId': tenant_id,
            'details': {
                'selectedRestaurants': filters.get('locations', []),
                'selectedWorkAreas': filters.get('workAreas', []),
                'selectedBaseDate': filters.get('baseDate', 'deliveryDate'),
                'selectedCategories': filters.get('categories', []),
                'selectedSubCategories': filters.get('subcategories', []),
                'selectedDepartments': filters.get('departments', []),
                'startDate': datetime.strptime(filters.get('startDate'), '%Y-%m-%d'),
                'endDate': datetime.strptime(filters.get('endDate'), '%Y-%m-%d')
            }
        }
    except (ValueError, TypeError) as e:
        raise ValueError(f"Invalid date format in filters: {str(e)}")

def _generate_dashboard(job: Dict[str, Any], dashboard_type: str, user_query: str, use_default_charts: bool, tenant_id: str = '') -> Dict[str, Any]:
    """Generate dashboard data based on type"""
    if dashboard_type == 'purchase':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = job['details'].get('selectedWorkAreas', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all']
        })

        df = grnStatusReport(job)
        return generate_purchase_dashboard(df)

    elif dashboard_type == 'inventory':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = job['details'].get('selectedWorkAreas', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all'],
            'type': 'store_variance'
        })
        df = store_variance(job)
        return generate_inventory_dashboard(df)

    elif dashboard_type == 'reconciliation':
        selected_categories = job['details'].get('selectedCategories', [])
        selected_subcategories = job['details'].get('selectedSubCategories', [])
        selected_work_areas = [job['details'].get('selectedWorkAreas', [])[-1]]
        selected_departments = job['details'].get('selectedDepartments', [])

        job['details'].update({
            'selectedCategories': selected_categories if selected_categories else ['all'],
            'selectedSubCategories': selected_subcategories if selected_subcategories else ['all'],
            'selectedVendors': [],
            'selectedWorkAreas': selected_work_areas if selected_work_areas else ['all'],
            'selectedDepartments': selected_departments if selected_departments else ['all'],
            'type': 'reconciliation'
        })

        # Get both store_variance and inventory_consumption data
        store_variance_df = store_variance(job)
        inventory_consumption_df = inventoryConsumptionNew(job)
        print(len(store_variance_df), "store_variance_df")
        print(len(inventory_consumption_df), "inventory_consumption_df")
        print(f"Selected departments: {selected_departments}")

        return generate_reconciliation_dashboard(
            store_variance_df,
            inventory_consumption_df,
            departments=selected_departments,
            tenant_id=tenant_id
        )

    else:
        # TODO: Add support for other dashboard types - smart_ask_dashboard(df, user_query, use_default_charts)
        raise ValueError(f"Invalid dashboard type: {dashboard_type}")
