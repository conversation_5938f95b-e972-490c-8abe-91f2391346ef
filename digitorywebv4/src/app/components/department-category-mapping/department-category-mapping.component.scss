.department-mapping-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  &.dialog-mode {
    padding: 16px;
    max-width: 800px;
  }
}

// Header Styles
.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;

  .header-content {
    flex: 1;
  }

  .header-title {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 500;
    color: #333;

    mat-icon {
      color: #ff9d4d;
    }
  }

  .header-subtitle {
    margin: 0;
    color: #666;
    font-size: 14px;
    line-height: 1.4;
  }

  .header-actions {
    .close-button {
      color: #666;
      
      &:hover {
        color: #333;
        background-color: #f5f5f5;
      }
    }
  }
}

// Loading Styles
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;

  mat-spinner {
    margin-bottom: 16px;
  }

  p {
    color: #666;
    margin: 0;
  }
}

// Validation Errors
.validation-errors {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  margin-bottom: 24px;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 8px;

  .error-icon {
    color: #d32f2f;
    margin-top: 2px;
  }

  .error-list {
    flex: 1;

    .error-message {
      margin: 0 0 4px 0;
      color: #d32f2f;
      font-size: 14px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Form Styles
.mapping-form {
  .mappings-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
}

// Mapping Card Styles
.mapping-card {
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;

  &:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    display: flex;
    align-items: flex-start;
    gap: 16px;
    margin-bottom: 16px;

    .department-selection {
      flex: 1;
    }

    .department-field {
      width: 100%;
    }

    .remove-button {
      color: #d32f2f;
      margin-top: 8px;

      &:hover:not(:disabled) {
        background-color: #ffebee;
      }

      &:disabled {
        color: #ccc;
      }
    }
  }

  .card-content {
    .categories-section {
      margin-bottom: 16px;

      .categories-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #333;
        font-size: 14px;
      }

      .categories-field {
        width: 100%;
      }
    }

    .selected-categories {
      .categories-chips {
        mat-chip-list {
          .category-chip {
            background-color: #e3f2fd;
            color: #1976d2;
            font-size: 12px;
          }
        }
      }
    }
  }
}

// Add Mapping Section
.add-mapping-section {
  display: flex;
  justify-content: center;
  padding: 16px;

  .add-mapping-button {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #ff9d4d;
    border-color: #ff9d4d;

    &:hover {
      background-color: #fff3e0;
    }
  }
}

// Summary Section
.mapping-summary {
  margin-top: 32px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 500;
    color: #333;
  }

  .summary-grid {
    display: grid;
    gap: 12px;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .summary-item {
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;

    .summary-department {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .summary-categories {
      .category-count {
        font-size: 12px;
        color: #666;
        font-weight: 500;
      }

      .category-list {
        font-size: 12px;
        color: #666;
        margin-top: 4px;
        line-height: 1.4;
      }
    }
  }
}

// Actions Section
.mapping-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;

  .cancel-button {
    color: #666;

    &:hover {
      background-color: #f5f5f5;
    }
  }

  .save-button {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #ff9d4d;
    color: white;

    &:hover:not(:disabled) {
      background-color: #ff8a33;
    }

    &:disabled {
      background-color: #ccc;
      color: #666;
    }

    .button-spinner {
      width: 20px !important;
      height: 20px !important;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .department-mapping-container {
    padding: 16px;

    &.dialog-mode {
      padding: 12px;
    }
  }

  .mapping-header {
    flex-direction: column;
    gap: 16px;

    .header-actions {
      align-self: flex-end;
    }
  }

  .mapping-card {
    padding: 16px;

    .card-header {
      flex-direction: column;
      gap: 12px;

      .remove-button {
        align-self: flex-end;
        margin-top: 0;
      }
    }
  }

  .mapping-actions {
    flex-direction: column-reverse;
    gap: 8px;

    button {
      width: 100%;
    }
  }

  .mapping-summary {
    .summary-grid {
      grid-template-columns: 1fr;
    }
  }
}
