import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { environment } from '../../environments/environment';

// ===== INTERFACES =====
export interface LoginCredentials {
  emailId: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  accountId: string;
  token: string;
  message?: string;
}

export interface Department {
  id: string;
  name: string;
  code?: string;
  description?: string;
  isActive?: boolean;
}

export interface DepartmentCategoryMapping {
  departmentId: string;
  departmentName: string;
  categories: string[];
}

export interface RoloposConfig {
  tenantId: string;
  emailId: string;
  password: string;
  accountId?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DepartmentService {
  private readonly rmsApiUrl = 'https://rms-api.digitory.com';
  private readonly baseUrl = environment.baseUrl;
  private readonly engineUrl = environment.engineUrl;
  
  // Cache for authentication tokens
  private authCache = new Map<string, { token: string; accountId: string; expiry: number }>();

  constructor(private readonly http: HttpClient) {}

  // ===== AUTHENTICATION METHODS =====
  /**
   * Get tenant credentials from MongoDB roloposconfigs collection
   */
  private getTenantCredentials(tenantId: string): Observable<LoginCredentials> {
    return this.http.post<any>(`${this.engineUrl}api/smart-dashboard/tenant-credentials`, { tenantId })
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            return {
              emailId: response.data.emailId,
              password: response.data.password
            };
          }
          throw new Error('Failed to retrieve tenant credentials');
        }),
        catchError(error => {
          console.error('Error fetching tenant credentials:', error);
          return throwError(() => new Error('Failed to retrieve tenant credentials'));
        })
      );
  }

  /**
   * Authenticate with RMS API using tenant credentials
   */
  private authenticateWithRMS(credentials: LoginCredentials): Observable<LoginResponse> {
    const headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'App-Id': 'inventory'
    });

    return this.http.post<any>(`${this.rmsApiUrl}/login`, credentials, { headers })
      .pipe(
        map(response => {
          if (response && response.accountId && response.token) {
            return {
              success: true,
              accountId: response.accountId,
              token: response.token
            };
          }
          throw new Error('Invalid login response');
        }),
        catchError(error => {
          console.error('RMS Authentication failed:', error);
          return throwError(() => new Error('Authentication failed'));
        })
      );
  }

  /**
   * Get authenticated token and account ID for tenant
   */
  private getAuthenticatedSession(tenantId: string): Observable<{ token: string; accountId: string }> {
    // Check cache first
    const cached = this.authCache.get(tenantId);
    if (cached && cached.expiry > Date.now()) {
      return new Observable(observer => {
        observer.next({ token: cached.token, accountId: cached.accountId });
        observer.complete();
      });
    }

    // Authenticate if not cached or expired
    return this.getTenantCredentials(tenantId).pipe(
      switchMap(credentials => this.authenticateWithRMS(credentials)),
      map(loginResponse => {
        // Cache the result for 1 hour
        this.authCache.set(tenantId, {
          token: loginResponse.token,
          accountId: loginResponse.accountId,
          expiry: Date.now() + (60 * 60 * 1000) // 1 hour
        });
        
        return {
          token: loginResponse.token,
          accountId: loginResponse.accountId
        };
      })
    );
  }

  // ===== DEPARTMENT METHODS =====
  /**
   * Get all departments for a tenant
   */
  getDepartments(tenantId: string): Observable<Department[]> {
    return this.getAuthenticatedSession(tenantId).pipe(
      switchMap(auth => {
        const headers = new HttpHeaders({
          'Authorization': `Bearer ${auth.token}`,
          'Content-Type': 'application/json'
        });

        return this.http.get<any>(`${this.rmsApiUrl}/account/${auth.accountId}/departments`, { headers });
      }),
      map(response => {
        if (response && Array.isArray(response)) {
          return response.map(dept => ({
            id: dept.id || dept._id,
            name: dept.name || dept.departmentName,
            code: dept.code || dept.departmentCode,
            description: dept.description,
            isActive: dept.isActive !== false
          }));
        }
        return [];
      }),
      catchError(error => {
        console.error('Error fetching departments:', error);
        return throwError(() => new Error('Failed to fetch departments'));
      })
    );
  }

  // ===== DEPARTMENT-CATEGORY MAPPING METHODS =====
  /**
   * Get department-category mappings for a tenant
   */
  getDepartmentCategoryMappings(tenantId: string): Observable<DepartmentCategoryMapping[]> {
    return this.http.get<any>(`${this.engineUrl}api/smart-dashboard/department-category-mappings/${tenantId}`)
      .pipe(
        map(response => {
          if (response.status === 'success' && response.data) {
            return response.data;
          }
          return [];
        }),
        catchError(error => {
          console.error('Error fetching department-category mappings:', error);
          return [];
        })
      );
  }

  /**
   * Save department-category mappings
   */
  saveDepartmentCategoryMappings(tenantId: string, mappings: DepartmentCategoryMapping[]): Observable<boolean> {
    const payload = {
      tenantId,
      mappings
    };

    return this.http.post<any>(`${this.engineUrl}api/smart-dashboard/department-category-mappings`, payload)
      .pipe(
        map(response => response.status === 'success'),
        catchError(error => {
          console.error('Error saving department-category mappings:', error);
          return throwError(() => new Error('Failed to save mappings'));
        })
      );
  }

  /**
   * Get categories mapped to a specific department
   */
  getCategoriesForDepartment(tenantId: string, departmentId: string): Observable<string[]> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.departmentId === departmentId);
        return mapping ? mapping.categories : [];
      })
    );
  }

  /**
   * Get department for a specific category
   */
  getDepartmentForCategory(tenantId: string, category: string): Observable<string | null> {
    return this.getDepartmentCategoryMappings(tenantId).pipe(
      map(mappings => {
        const mapping = mappings.find(m => m.categories.includes(category));
        return mapping ? mapping.departmentId : null;
      })
    );
  }

  // ===== UTILITY METHODS =====
  /**
   * Clear authentication cache for a tenant
   */
  clearAuthCache(tenantId?: string): void {
    if (tenantId) {
      this.authCache.delete(tenantId);
    } else {
      this.authCache.clear();
    }
  }

  /**
   * Validate department-category mapping rules
   * - Single department can have multiple categories
   * - Single category can only be mapped to one department
   */
  validateMappings(mappings: DepartmentCategoryMapping[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const categoryToDepartment = new Map<string, string>();

    for (const mapping of mappings) {
      for (const category of mapping.categories) {
        if (categoryToDepartment.has(category)) {
          const existingDept = categoryToDepartment.get(category);
          if (existingDept !== mapping.departmentId) {
            errors.push(`Category "${category}" is mapped to multiple departments: "${existingDept}" and "${mapping.departmentName}"`);
          }
        } else {
          categoryToDepartment.set(category, mapping.departmentId);
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
